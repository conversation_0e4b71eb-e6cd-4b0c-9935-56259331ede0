# zhuiguangSYSTEM 追光系统 - 项目概述与硬件平台分析

## 1. 项目概述

### 1.1 项目简介

zhuiguangSYSTEM（追光系统）是一个基于STM32F407VET6微控制器的智能追光控制系统。该系统集成了多种传感器和执行器，能够实现自动光源跟踪和手动控制两种工作模式，广泛应用于太阳能板追踪、摄影设备自动跟光、教学演示等场景。

### 1.2 设计目标

- **高精度追光**：通过4路光敏传感器实现精确的光源定位和跟踪
- **多模式控制**：支持PS2手柄手动控制和自动追光模式切换
- **实时响应**：基于中断驱动的实时控制架构，响应时间<1ms
- **稳定可靠**：采用闭环控制算法，确保系统稳定运行
- **模块化设计**：BSP层硬件抽象，便于功能扩展和移植

### 1.3 技术指标

| 指标项目 | 技术参数 |
|---------|---------|
| 主控芯片 | STM32F407VET6 |
| 工作频率 | 168MHz |
| 追光精度 | ±1° |
| 响应时间 | <1ms |
| 控制方式 | 手动/自动双模式 |
| 通信接口 | UART、I2C、SPI模拟 |
| 工作电压 | 3.3V/5V |
| 工作温度 | -20°C ~ +70°C |

### 1.4 应用场景

- **太阳能追踪系统**：自动跟踪太阳位置，提高发电效率
- **摄影设备控制**：自动跟踪光源，优化拍摄效果
- **教学演示平台**：展示自动控制和传感器融合技术
- **机器人视觉系统**：作为视觉跟踪模块的硬件基础

## 2. 硬件平台分析

### 2.1 主控芯片 - STM32F407VET6

#### 2.1.1 芯片特性

STM32F407VET6是意法半导体推出的高性能ARM Cortex-M4微控制器，具有以下核心特性：

- **处理器内核**：ARM Cortex-M4 32位RISC内核
- **工作频率**：最高168MHz
- **浮点运算单元**：单精度FPU，支持IEEE 754标准
- **存储器配置**：
  - Flash存储器：512KB
  - SRAM：192KB（包含64KB CCM）
  - 备份SRAM：4KB
- **封装形式**：LQFP100
- **工作电压**：1.8V ~ 3.6V
- **工作温度**：-40°C ~ +85°C

#### 2.1.2 内存映射

| 存储区域 | 地址范围 | 大小 | 用途 |
|---------|---------|------|------|
| Flash | 0x08000000-0x0807FFFF | 512KB | 程序存储 |
| SRAM1 | 0x20000000-0x2001BFFF | 112KB | 数据存储 |
| SRAM2 | 0x2001C000-0x2001FFFF | 16KB | 数据存储 |
| CCM | 0x10000000-0x1000FFFF | 64KB | 核心耦合存储 |
| 备份SRAM | 0x40024000-0x40024FFF | 4KB | 备份数据 |

### 2.2 系统时钟配置

#### 2.2.1 时钟树结构

系统采用外部8MHz晶振作为时钟源，通过PLL倍频至168MHz：

```
HSE (8MHz) → PLL → SYSCLK (168MHz)
├── AHB (168MHz) → HCLK
├── APB1 (42MHz) → PCLK1, TIM_CLK1 (84MHz)
└── APB2 (84MHz) → PCLK2, TIM_CLK2 (168MHz)
```

#### 2.2.2 PLL配置参数

| 参数 | 值 | 说明 |
|------|----|----- |
| PLL_M | 4 | 输入分频系数 |
| PLL_N | 168 | 倍频系数 |
| PLL_P | 2 | 主输出分频 |
| PLL_Q | 4 | USB等外设分频 |

**计算公式**：SYSCLK = HSE × (PLL_N / PLL_M) / PLL_P = 8 × (168/4) / 2 = 168MHz

### 2.3 外设资源配置

#### 2.3.1 定时器配置

| 定时器 | 功能 | 通道配置 | 频率 |
|--------|------|---------|------|
| TIM1 | 编码器接口 | CH1(PE9), CH2(PE11) | 84MHz |
| TIM3 | 编码器接口 | CH1(PC6), CH2(PB5) | 84MHz |
| TIM4 | PWM输出(舵机) | CH1-4(PD12-15) | 84MHz |
| TIM5 | PWM输出(电机) | CH3(PA2), CH4(PA3) | 84MHz |
| TIM9 | 系统定时 | 内部时钟源 | 168MHz |
| TIM12 | PS2通信定时 | 内部时钟源 | 84MHz |

#### 2.3.2 ADC配置

| 通道 | 引脚 | 功能 | 采样率 |
|------|------|------|--------|
| ADC1_IN8 | PB0 | 光敏传感器1 | 3周期 |
| ADC1_IN9 | PB1 | 光敏传感器2 | 3周期 |
| ADC1_IN10 | PC0 | 光敏传感器3 | 3周期 |
| ADC1_IN11 | PC1 | 光敏传感器4 | 3周期 |

**DMA配置**：DMA2_Stream0，循环模式，16位数据对齐

#### 2.3.3 通信接口

| 接口 | 引脚 | 功能 | 配置 |
|------|------|------|------|
| USART1 | PA9(TX), PA10(RX) | 调试串口 | 异步模式 |
| I2C(模拟) | PB3(SDA), PB4(SCL) | MPU6050通信 | 开漏输出 |
| SPI(模拟) | PA4-7 | PS2手柄通信 | GPIO模拟 |

### 2.4 引脚分配表

#### 2.4.1 传感器接口

| 引脚 | 功能 | 方向 | 配置 |
|------|------|------|------|
| PB0 | 光敏传感器1 | 输入 | ADC_IN8 |
| PB1 | 光敏传感器2 | 输入 | ADC_IN9 |
| PC0 | 光敏传感器3 | 输入 | ADC_IN10 |
| PC1 | 光敏传感器4 | 输入 | ADC_IN11 |
| PB3 | MPU6050_SDA | 双向 | 开漏输出 |
| PB4 | MPU6050_SCL | 输出 | 开漏输出 |

#### 2.4.2 执行器接口

| 引脚 | 功能 | 方向 | 配置 |
|------|------|------|------|
| PD12 | 舵机1_PWM | 输出 | TIM4_CH1 |
| PD13 | 舵机2_PWM | 输出 | TIM4_CH2 |
| PD14 | 舵机3_PWM | 输出 | TIM4_CH3 |
| PD15 | 舵机4_PWM | 输出 | TIM4_CH4 |
| PA2 | 电机A_PWM | 输出 | TIM5_CH3 |
| PA3 | 电机B_PWM | 输出 | TIM5_CH4 |
| PD8 | 电机方向控制3 | 输出 | GPIO |
| PD9 | 电机方向控制4 | 输出 | GPIO |

#### 2.4.3 人机接口

| 引脚 | 功能 | 方向 | 配置 |
|------|------|------|------|
| PA4 | PS2_DAT | 输入 | 上拉输入 |
| PA5 | PS2_CMD | 输出 | GPIO输出 |
| PA6 | PS2_CS | 输出 | 高速GPIO |
| PA7 | PS2_CLK | 输出 | GPIO输出 |
| PA9 | UART_TX | 输出 | USART1 |
| PA10 | UART_RX | 输入 | USART1 |

#### 2.4.4 编码器接口

| 引脚 | 功能 | 方向 | 配置 |
|------|------|------|------|
| PE9 | 编码器1_A | 输入 | TIM1_CH1 |
| PE11 | 编码器1_B | 输入 | TIM1_CH2 |
| PC6 | 编码器2_A | 输入 | TIM3_CH1 |
| PB5 | 编码器2_B | 输入 | TIM3_CH2 |

### 2.5 中断优先级配置

| 中断源 | 优先级 | 功能 |
|--------|--------|------|
| SysTick | 15 | 系统滴答定时器 |
| TIM9 | 0 | 传感器数据采集 |
| TIM12 | 0 | PS2通信定时 |
| DMA2_Stream0 | 0 | ADC数据传输 |
| USART1 | 0 | 串口通信 |

### 2.6 硬件设计优势

1. **高性能处理**：168MHz主频配合FPU，满足实时控制需求
2. **丰富外设**：多路定时器、ADC、DMA等外设支持复杂应用
3. **低功耗设计**：支持多种低功耗模式，适合电池供电
4. **扩展性强**：LQFP100封装提供丰富的GPIO资源
5. **开发友好**：完善的HAL库支持，开发效率高

## 3. 系统框图

```
┌─────────────────────────────────────────────────────────────┐
│                    STM32F407VET6 主控制器                    │
├─────────────────────────────────────────────────────────────┤
│  ARM Cortex-M4 @ 168MHz + FPU                             │
│  Flash: 512KB | SRAM: 192KB | CCM: 64KB                   │
└─────────────────┬───────────────────────┬───────────────────┘
                  │                       │
        ┌─────────▼─────────┐   ┌─────────▼─────────┐
        │    传感器模块      │   │    执行器模块      │
        ├───────────────────┤   ├───────────────────┤
        │ • 4路光敏传感器   │   │ • 3路舵机控制     │
        │ • MPU6050陀螺仪   │   │ • 2路电机驱动     │
        │ • 2路编码器       │   │ • PWM信号生成     │
        └───────────────────┘   └───────────────────┘
                  │                       │
        ┌─────────▼─────────┐   ┌─────────▼─────────┐
        │    通信模块        │   │    控制算法       │
        ├───────────────────┤   ├───────────────────┤
        │ • PS2手柄通信     │   │ • PID控制器       │
        │ • UART调试接口    │   │ • 追光算法        │
        │ • I2C传感器接口   │   │ • 姿态解算        │
        └───────────────────┘   └───────────────────┘
```

本章节完成了zhuiguangSYSTEM追光系统的项目概述和硬件平台的详细分析，为后续的软件架构和功能模块分析奠定了基础。
