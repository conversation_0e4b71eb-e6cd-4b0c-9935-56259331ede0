/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.h
  * @brief          : Header for main.c file.
  *                   This file contains the common defines of the application.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __MAIN_H
#define __MAIN_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx_hal.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Exported types ------------------------------------------------------------*/
/* USER CODE BEGIN ET */

/* USER CODE END ET */

/* Exported constants --------------------------------------------------------*/
/* USER CODE BEGIN EC */

/* USER CODE END EC */

/* Exported macro ------------------------------------------------------------*/
/* USER CODE BEGIN EM */

/* USER CODE END EM */

/* Exported functions prototypes ---------------------------------------------*/
void Error_Handler(void);

/* USER CODE BEGIN EFP */

/* USER CODE END EFP */

/* Private defines -----------------------------------------------------------*/
#define PS2_DAT_Pin GPIO_PIN_4
#define PS2_DAT_GPIO_Port GPIOA
#define PS2_CMD_Pin GPIO_PIN_5
#define PS2_CMD_GPIO_Port GPIOA
#define PS2_CS_Pin GPIO_PIN_6
#define PS2_CS_GPIO_Port GPIOA
#define PS2_CLK_Pin GPIO_PIN_7
#define PS2_CLK_GPIO_Port GPIOA
#define Dir1_Pin GPIO_PIN_12
#define Dir1_GPIO_Port GPIOB
#define Dir2_Pin GPIO_PIN_13
#define Dir2_GPIO_Port GPIOB
#define Dir3_Pin GPIO_PIN_14
#define Dir3_GPIO_Port GPIOB
#define Dir4_Pin GPIO_PIN_15
#define Dir4_GPIO_Port GPIOB
#define PWM4_Pin GPIO_PIN_8
#define PWM4_GPIO_Port GPIOD
#define PWM3_Pin GPIO_PIN_9
#define PWM3_GPIO_Port GPIOD
#define SDA_Pin GPIO_PIN_3
#define SDA_GPIO_Port GPIOB
#define SCL_Pin GPIO_PIN_4
#define SCL_GPIO_Port GPIOB

/* USER CODE BEGIN Private defines */
void delay_us(__IO uint32_t delay);
/* USER CODE END Private defines */

#ifdef __cplusplus
}
#endif

#endif /* __MAIN_H */
